#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检验表合并脚本
将抽样检验表和全部检验表合并为统一的来料检验表
"""

import mysql.connector
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config

def get_db_connection():
    """建立与指定数据库的连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def backup_existing_tables():
    """备份现有的检验表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 备份抽样检验表
        backup_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        cursor.execute(f"CREATE TABLE sampling_inspection_backup_{backup_time} AS SELECT * FROM sampling_inspection")
        print(f"✅ 抽样检验表已备份为: sampling_inspection_backup_{backup_time}")
        
        # 备份全部检验表
        cursor.execute(f"CREATE TABLE full_inspection_backup_{backup_time} AS SELECT * FROM full_inspection")
        print(f"✅ 全部检验表已备份为: full_inspection_backup_{backup_time}")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def create_unified_inspection_table():
    """创建统一的来料检验表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 创建新的统一检验表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS incoming_inspection (
                id INT AUTO_INCREMENT,
                report_code VARCHAR(50) UNIQUE,
                material_number VARCHAR(50) NOT NULL,
                material_name VARCHAR(100) NOT NULL,
                specification VARCHAR(255),
                material_type VARCHAR(100),
                color VARCHAR(50),
                supplier VARCHAR(100),
                purchase_order VARCHAR(50) NOT NULL,
                receipt_date DATE NOT NULL,
                inspection_date DATE NOT NULL,
                inspection_type ENUM('sampling', 'full') NOT NULL DEFAULT 'sampling' COMMENT '检验类型：sampling-抽样检验，full-全部检验',
                total_quantity INT NOT NULL,
                sample_quantity INT NULL COMMENT '抽样数量（仅抽样检验使用）',
                qualified_quantity INT NOT NULL,
                defect_quantity INT NOT NULL,
                defect_issues TEXT,
                inspector VARCHAR(50),
                batch_number VARCHAR(100) COMMENT '批次号',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                INDEX idx_material_number (material_number),
                INDEX idx_supplier (supplier),
                INDEX idx_inspection_type (inspection_type),
                INDEX idx_dates (inspection_date, receipt_date),
                INDEX idx_search (material_number, supplier, inspection_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一来料检验记录表'
        """)
        print("✅ 统一来料检验表创建成功")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ 创建统一检验表失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def migrate_data():
    """迁移现有数据到统一表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 迁移抽样检验数据
        cursor.execute("""
            INSERT INTO incoming_inspection (
                report_code, material_number, material_name, specification, material_type, color,
                supplier, purchase_order, receipt_date, inspection_date, inspection_type,
                total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                defect_issues, inspector, created_at
            )
            SELECT 
                report_code, material_number, material_name, specification, material_type, color,
                supplier, purchase_order, receipt_date, inspection_date, 'sampling',
                total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                defect_issues, inspector, created_at
            FROM sampling_inspection
        """)
        sampling_count = cursor.rowcount
        print(f"✅ 抽样检验数据迁移完成: {sampling_count} 条记录")
        
        # 迁移全部检验数据
        cursor.execute("""
            INSERT INTO incoming_inspection (
                report_code, material_number, material_name, specification, material_type, color,
                supplier, purchase_order, receipt_date, inspection_date, inspection_type,
                total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                defect_issues, inspector, created_at
            )
            SELECT 
                report_code, material_number, material_name, specification, material_type, color,
                supplier, purchase_order, receipt_date, inspection_date, 'full',
                total_quantity, NULL, qualified_quantity, defect_quantity,
                defect_issues, inspector, created_at
            FROM full_inspection
        """)
        full_count = cursor.rowcount
        print(f"✅ 全部检验数据迁移完成: {full_count} 条记录")
        
        conn.commit()
        return True, sampling_count, full_count
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False, 0, 0
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def verify_migration():
    """验证数据迁移的正确性"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查原表记录数
        cursor.execute("SELECT COUNT(*) FROM sampling_inspection")
        sampling_original = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM full_inspection")
        full_original = cursor.fetchone()[0]
        
        # 检查新表记录数
        cursor.execute("SELECT COUNT(*) FROM incoming_inspection WHERE inspection_type = 'sampling'")
        sampling_migrated = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM incoming_inspection WHERE inspection_type = 'full'")
        full_migrated = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM incoming_inspection")
        total_migrated = cursor.fetchone()[0]
        
        print("\n📊 数据迁移验证:")
        print(f"原抽样检验记录: {sampling_original} -> 迁移后: {sampling_migrated}")
        print(f"原全部检验记录: {full_original} -> 迁移后: {full_migrated}")
        print(f"总计迁移记录: {total_migrated}")
        
        if sampling_original == sampling_migrated and full_original == full_migrated:
            print("✅ 数据迁移验证通过")
            return True
        else:
            print("❌ 数据迁移验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始检验表合并流程")
    print("=" * 50)
    
    # 1. 备份现有表
    print("1. 备份现有检验表...")
    if not backup_existing_tables():
        print("❌ 备份失败，程序退出")
        return False
    
    # 2. 创建统一检验表
    print("\n2. 创建统一来料检验表...")
    if not create_unified_inspection_table():
        print("❌ 创建统一表失败，程序退出")
        return False
    
    # 3. 迁移数据
    print("\n3. 迁移现有数据...")
    success, sampling_count, full_count = migrate_data()
    if not success:
        print("❌ 数据迁移失败，程序退出")
        return False
    
    # 4. 验证迁移
    print("\n4. 验证数据迁移...")
    if not verify_migration():
        print("❌ 验证失败，请检查数据")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 检验表合并完成！")
    print(f"\n📋 合并结果:")
    print(f"  - 抽样检验记录: {sampling_count} 条")
    print(f"  - 全部检验记录: {full_count} 条")
    print(f"  - 总计: {sampling_count + full_count} 条")
    print(f"\n📝 下一步:")
    print(f"  1. 测试新表功能是否正常")
    print(f"  2. 确认无误后可删除原表: DROP TABLE sampling_inspection, full_inspection")
    print(f"  3. 更新应用代码使用新的统一表")
    
    return True

if __name__ == "__main__":
    main()
