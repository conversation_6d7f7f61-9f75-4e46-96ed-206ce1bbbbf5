{% extends "base.html" %}

{% block title %}来料检验记录 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .header-left h1 {
        font-size: 24px;
        margin: 0;
        color: #333;
    }

    .header-right {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        font-size: 14px;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-success {
        background-color: #28a745;
        color: white;
    }

    .filters {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .filter-row {
        display: flex;
        gap: 15px;
        align-items: center;
        margin-bottom: 10px;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .filter-group label {
        font-size: 12px;
        font-weight: bold;
        color: #555;
    }

    .filter-group input, .filter-group select {
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 12px;
    }

    .table-container {
        background: white;
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th {
        background-color: #f8f9fa;
        padding: 12px 8px;
        text-align: left;
        font-weight: bold;
        border-bottom: 2px solid #dee2e6;
        font-size: 13px;
    }

    .data-table td {
        padding: 10px 8px;
        border-bottom: 1px solid #dee2e6;
        font-size: 12px;
    }

    .data-table tr:hover {
        background-color: #f5f5f5;
    }

    .inspection-type-badge {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: bold;
    }

    .badge-sampling {
        background-color: #e3f2fd;
        color: #1976d2;
    }

    .badge-full {
        background-color: #f3e5f5;
        color: #7b1fa2;
    }

    .action-btn {
        padding: 4px 8px;
        margin: 0 2px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        text-decoration: none;
    }

    .btn-view {
        background-color: #17a2b8;
        color: white;
    }

    .btn-edit {
        background-color: #ffc107;
        color: #212529;
    }

    .btn-delete {
        background-color: #dc3545;
        color: white;
    }

    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .pagination a, .pagination span {
        padding: 8px 12px;
        margin: 0 2px;
        text-decoration: none;
        border: 1px solid #ddd;
        border-radius: 3px;
    }

    .pagination .current {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .loading {
        text-align: center;
        padding: 20px;
        color: #666;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="header-left">
        <h1>来料检验记录</h1>
    </div>
    <div class="header-right">
        <a href="{{ url_for('incoming_inspection.new_incoming_inspection') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 新增检验
        </a>
        <a href="{{ url_for('incoming_inspection.batch_import_sampling') }}" class="btn btn-success">
            <i class="fas fa-upload"></i> 批量导入
        </a>
    </div>
</div>

<!-- 筛选区域 -->
<div class="filters">
    <form method="GET" id="filter-form">
        <div class="filter-row">
            <div class="filter-group">
                <label>检验类型</label>
                <select name="inspection_type" id="inspection_type">
                    <option value="">全部</option>
                    <option value="sampling" {{ 'selected' if inspection_type == 'sampling' }}>抽样检验</option>
                    <option value="full" {{ 'selected' if inspection_type == 'full' }}>全部检验</option>
                </select>
            </div>
            <div class="filter-group">
                <label>开始日期</label>
                <input type="date" name="start_date" value="{{ start_date }}" id="start_date">
            </div>
            <div class="filter-group">
                <label>结束日期</label>
                <input type="date" name="end_date" value="{{ end_date }}" id="end_date">
            </div>
            <div class="filter-group">
                <label>物料料号</label>
                <input type="text" name="material_number" value="{{ material_number }}" placeholder="输入料号">
            </div>
            <div class="filter-group">
                <label>供应商</label>
                <input type="text" name="supplier" value="{{ supplier }}" placeholder="输入供应商">
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn btn-primary">搜索</button>
            </div>
        </div>
    </form>
</div>

<!-- 数据表格 -->
<div class="table-container">
    <table class="data-table" id="data-table">
        <thead>
            <tr>
                <th>报告编码</th>
                <th>检验类型</th>
                <th>物料料号</th>
                <th>物料名称</th>
                <th>供应商</th>
                <th>检验日期</th>
                <th>总数量</th>
                <th>抽样数量</th>
                <th>合格数量</th>
                <th>不良数量</th>
                <th>不良率</th>
                <th>检验员</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="data-tbody">
            <tr>
                <td colspan="13" class="loading">正在加载数据...</td>
            </tr>
        </tbody>
    </table>
</div>

<!-- 分页 -->
<div class="pagination" id="pagination">
    <!-- 分页内容将通过JavaScript动态生成 -->
</div>

<script>
let currentPage = {{ page }};
let currentFilters = {
    inspection_type: '{{ inspection_type }}',
    start_date: '{{ start_date }}',
    end_date: '{{ end_date }}',
    material_number: '{{ material_number }}',
    supplier: '{{ supplier }}'
};

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    loadInspectionRecords();
    
    // 绑定筛选表单提交事件
    document.getElementById('filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        updateFiltersFromForm();
        loadInspectionRecords();
    });
});

function updateFiltersFromForm() {
    currentFilters = {
        inspection_type: document.getElementById('inspection_type').value,
        start_date: document.getElementById('start_date').value,
        end_date: document.getElementById('end_date').value,
        material_number: document.querySelector('input[name="material_number"]').value,
        supplier: document.querySelector('input[name="supplier"]').value
    };
}

function loadInspectionRecords() {
    const params = new URLSearchParams({
        page: currentPage,
        per_page: 20,
        ...currentFilters
    });
    
    fetch(`/incoming/api/inspections?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderTable(data.records);
                renderPagination(data.page, data.total_pages, data.total_records);
            } else {
                showError('加载数据失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('网络错误，请重试');
        });
}

function renderTable(records) {
    const tbody = document.getElementById('data-tbody');
    
    if (records.length === 0) {
        tbody.innerHTML = '<tr><td colspan="13" class="empty-state">暂无检验记录</td></tr>';
        return;
    }
    
    tbody.innerHTML = records.map(record => {
        const inspectionTypeBadge = record.inspection_type === 'sampling' 
            ? '<span class="inspection-type-badge badge-sampling">抽样检验</span>'
            : '<span class="inspection-type-badge badge-full">全部检验</span>';
            
        const sampleQuantity = record.sample_quantity || '-';
        const defectRate = record.inspection_type === 'sampling' && record.sample_quantity > 0
            ? ((record.defect_quantity / record.sample_quantity) * 100).toFixed(2) + '%'
            : record.total_quantity > 0 
                ? ((record.defect_quantity / record.total_quantity) * 100).toFixed(2) + '%'
                : '0%';
        
        return `
            <tr>
                <td>${record.report_code || '-'}</td>
                <td>${inspectionTypeBadge}</td>
                <td>${record.material_number}</td>
                <td>${record.material_name}</td>
                <td>${record.supplier}</td>
                <td>${record.inspection_date}</td>
                <td>${record.total_quantity}</td>
                <td>${sampleQuantity}</td>
                <td>${record.qualified_quantity}</td>
                <td>${record.defect_quantity}</td>
                <td>${defectRate}</td>
                <td>${record.inspector || '-'}</td>
                <td>
                    <a href="/incoming/detail_inspection?id=${record.id}&type=${record.inspection_type}" class="action-btn btn-view">查看</a>
                    <button onclick="deleteRecord(${record.id})" class="action-btn btn-delete">删除</button>
                </td>
            </tr>
        `;
    }).join('');
}

function renderPagination(page, totalPages, totalRecords) {
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    if (page > 1) {
        html += `<a href="#" onclick="changePage(${page - 1})">上一页</a>`;
    }
    
    // 页码
    for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
        if (i === page) {
            html += `<span class="current">${i}</span>`;
        } else {
            html += `<a href="#" onclick="changePage(${i})">${i}</a>`;
        }
    }
    
    // 下一页
    if (page < totalPages) {
        html += `<a href="#" onclick="changePage(${page + 1})">下一页</a>`;
    }
    
    pagination.innerHTML = html;
}

function changePage(page) {
    currentPage = page;
    loadInspectionRecords();
}

function deleteRecord(id) {
    if (!confirm('确定要删除这条检验记录吗？')) {
        return;
    }
    
    fetch(`/incoming/api/delete_inspection/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('删除成功');
            loadInspectionRecords();
        } else {
            alert('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请重试');
    });
}

function showError(message) {
    const tbody = document.getElementById('data-tbody');
    tbody.innerHTML = `<tr><td colspan="13" class="empty-state" style="color: red;">${message}</td></tr>`;
}
</script>
{% endblock %}
