#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据表创建脚本
用于创建品质中心管理系统的所有数据表
"""

import mysql.connector
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config

def get_mysql_connection():
    """建立与MySQL服务器的连接（不指定数据库）"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD
    )

def get_db_connection():
    """建立与指定数据库的连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def create_database():
    """创建数据库"""
    try:
        config = Config()
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor()
        
        # 创建数据库（如果不存在）
        mysql_cursor.execute(f"CREATE DATABASE IF NOT EXISTS {config.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"数据库 {config.DB_NAME} 创建成功或已存在")
        
        mysql_cursor.close()
        mysql_conn.close()
        return True
        
    except Exception as e:
        print(f"创建数据库失败: {e}")
        return False

def create_tables():
    """创建所有数据表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        print("连接到数据库成功，开始创建表...")
        
        # 创建统一的来料检验记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS incoming_inspection (
                id INT AUTO_INCREMENT,
                report_code VARCHAR(50) UNIQUE,
                material_number VARCHAR(50) NOT NULL,
                material_name VARCHAR(100) NOT NULL,
                specification VARCHAR(255),
                material_type VARCHAR(100),
                color VARCHAR(50),
                supplier VARCHAR(100),
                purchase_order VARCHAR(50) NOT NULL,
                receipt_date DATE NOT NULL,
                inspection_date DATE NOT NULL,
                inspection_type ENUM('sampling', 'full') NOT NULL DEFAULT 'sampling' COMMENT '检验类型：sampling-抽样检验，full-全部检验',
                total_quantity INT NOT NULL,
                sample_quantity INT NULL COMMENT '抽样数量（仅抽样检验使用）',
                qualified_quantity INT NOT NULL,
                defect_quantity INT NOT NULL,
                defect_issues TEXT,
                inspector VARCHAR(50),
                batch_number VARCHAR(100) COMMENT '批次号',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一来料检验记录表'
        """)
        print("✅ 统一来料检验表创建成功")
        
        # 创建物料表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS materials (
                id INT AUTO_INCREMENT,
                material_number VARCHAR(50) UNIQUE NOT NULL,
                material_name VARCHAR(100) NOT NULL,
                specification VARCHAR(100),
                material_type VARCHAR(50),
                color VARCHAR(30),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            )
        """)
        print("✅ 物料表创建成功")
        
        # 创建车间异常记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS workshop_abnormal (
                id INT AUTO_INCREMENT,
                product_number VARCHAR(50) NOT NULL,
                product_name VARCHAR(100) NOT NULL,
                workshop VARCHAR(50) NOT NULL,
                process_name VARCHAR(100) NOT NULL,
                abnormal_type VARCHAR(50) NOT NULL,
                abnormal_details TEXT,
                record_date DATE NOT NULL,
                recorder VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            )
        """)
        print("✅ 车间异常记录表创建成功")
        
        # 创建系统设置表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INT AUTO_INCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                description VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            )
        """)
        print("✅ 系统设置表创建成功")
        
        # 插入默认系统设置
        cursor.execute("""
            INSERT IGNORE INTO system_settings (setting_key, setting_value, description)
            VALUES ('document_path', 'D:\\Documents\\Materials', '文档文件夹路径')
        """)
        
        conn.commit()
        print("✅ 所有数据表创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据表创建失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def create_indexes():
    """创建索引以优化查询性能"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        print("开始创建索引...")
        
        # 为统一来料检验表添加索引
        indexes = [
            "CREATE INDEX idx_incoming_material_number ON incoming_inspection(material_number)",
            "CREATE INDEX idx_incoming_supplier ON incoming_inspection(supplier)",
            "CREATE INDEX idx_incoming_inspection_type ON incoming_inspection(inspection_type)",
            "CREATE INDEX idx_incoming_dates ON incoming_inspection(inspection_date, receipt_date)",
            "CREATE INDEX idx_incoming_search ON incoming_inspection(material_number, supplier, inspection_date)",
            "CREATE INDEX idx_incoming_batch ON incoming_inspection(batch_number)",
            
            # 为车间异常记录表添加索引
            "CREATE INDEX idx_workshop_abnormal_product ON workshop_abnormal(product_number)",
            "CREATE INDEX idx_workshop_abnormal_workshop ON workshop_abnormal(workshop)",
            "CREATE INDEX idx_workshop_abnormal_date ON workshop_abnormal(record_date)",
            "CREATE INDEX idx_workshop_abnormal_type ON workshop_abnormal(abnormal_type)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                print(f"✅ 索引创建成功: {index_sql.split('ON')[0].split('INDEX')[1].strip()}")
            except mysql.connector.Error as e:
                if e.errno == 1061:  # 索引已存在
                    print(f"⚠️  索引已存在: {index_sql.split('ON')[0].split('INDEX')[1].strip()}")
                else:
                    print(f"❌ 索引创建失败: {e}")
        
        conn.commit()
        print("✅ 索引创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 索引创建失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始初始化品质中心管理系统数据库")
    print("=" * 50)
    
    # 1. 创建数据库
    if not create_database():
        print("❌ 数据库创建失败，程序退出")
        return False
    
    # 2. 创建数据表
    if not create_tables():
        print("❌ 数据表创建失败，程序退出")
        return False
    
    # 3. 创建索引
    if not create_indexes():
        print("❌ 索引创建失败，程序退出")
        return False
    
    print("=" * 50)
    print("🎉 数据库初始化完成！")
    print("\n📋 已创建的数据表:")
    print("  - incoming_inspection    (统一来料检验记录)")
    print("  - materials              (物料信息)")
    print("  - workshop_abnormal      (车间异常记录)")
    print("  - system_settings        (系统设置)")
    print("\n🔍 已创建的索引:")
    print("  - 物料编号索引")
    print("  - 供应商索引")
    print("  - 检验类型索引")
    print("  - 日期索引")
    print("  - 批次号索引")
    print("  - 复合搜索索引")
    
    return True

if __name__ == "__main__":
    main()
