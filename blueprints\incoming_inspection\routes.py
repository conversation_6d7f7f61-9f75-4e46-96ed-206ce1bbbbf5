from flask import render_template, request, jsonify, current_app
from . import incoming_inspection_bp
from db_config import get_db_connection
import os
import sys
from werkzeug.utils import secure_filename
import json
from datetime import datetime, timedelta

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))
from report_code_generator import generate_report_code

# ------ 统一来料检验蓝图路由 ------

# 来料检验首页
@incoming_inspection_bp.route('/')
def index():
    return render_template('index.html')

# 新增检验记录页面
@incoming_inspection_bp.route('/new_inspection')
def new_inspection():
    return render_template('new_inspection.html')

# 检验记录详情页面
@incoming_inspection_bp.route('/detail_inspection')
def detail_inspection_page():
    record_id = request.args.get('id')
    inspection_type = request.args.get('type', 'sampling')
    if not record_id:
        return "缺少记录ID", 400
    return render_template('detail_inspection.html',
                          record_id=record_id,
                          inspection_type=inspection_type)

# 统一批量导入待检页面
@incoming_inspection_bp.route('/batch_import_sampling')
def batch_import_sampling():
    return render_template('batch_import_sampling.html')

# 批量导入待检 - 全部检验（重定向到统一页面）
@incoming_inspection_bp.route('/batch_import_full')
def batch_import_full():
    return render_template('batch_import_sampling.html')

# 待检清单
@incoming_inspection_bp.route('/pending_list')
def pending_list():
    inspection_type = request.args.get('type', 'sampling')
    return render_template('pending_list.html', inspection_type=inspection_type)

# 统一来料检验记录页面
@incoming_inspection_bp.route('/records')
def inspection_records():
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 获取筛选参数
    inspection_type = request.args.get('inspection_type', '')  # 检验类型筛选
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    material_number = request.args.get('material_number', '')
    supplier = request.args.get('supplier', '')
    
    # 如果没有提供日期，默认设置为近7天
    if not start_date or not end_date:
        if not start_date:
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
    
    return render_template('incoming_inspection_records.html',
                          page=page,
                          per_page=per_page,
                          inspection_type=inspection_type,
                          start_date=start_date,
                          end_date=end_date,
                          material_number=material_number,
                          supplier=supplier)

# 新增来料检验记录页面
@incoming_inspection_bp.route('/new')
def new_incoming_inspection():
    return render_template('new_incoming_inspection.html')

# 添加检验记录API
@incoming_inspection_bp.route('/api/add_inspection', methods=['POST'])
def add_inspection():
    try:
        # 获取基本表单数据
        material_number = request.form.get('material_number')
        material_name = request.form.get('material_name')
        specification = request.form.get('specification')
        material_type = request.form.get('material_type')
        color = request.form.get('color')
        supplier = request.form.get('supplier')
        purchase_order = request.form.get('purchase_order')
        receipt_date = request.form.get('receipt_date')
        inspection_date = request.form.get('inspection_date')
        batch_number = request.form.get('batch_number', '')
        inspection_type = request.form.get('inspection_type', 'sampling')  # 检验类型
        total_quantity = int(request.form.get('total_quantity'))
        defect_quantity = int(request.form.get('defect_quantity'))

        # 根据检验类型获取不同的数量字段
        if inspection_type == 'sampling':
            sample_quantity = int(request.form.get('sample_quantity'))
            qualified_quantity = sample_quantity - defect_quantity
        else:  # full
            qualified_quantity = int(request.form.get('qualified_quantity'))
            sample_quantity = None
            
        defect_issues = request.form.get('defect_issues')
        inspector = request.form.get('inspector')
        
        # 生成报告编码
        report_code = generate_report_code(inspection_type, inspection_date)

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入到统一的来料检验表
        cursor.execute("""
            INSERT INTO incoming_inspection (
                report_code, material_number, material_name, specification, material_type, color,
                supplier, purchase_order, receipt_date, inspection_date, inspection_type,
                total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                defect_issues, inspector, batch_number
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            report_code, material_number, material_name, specification, material_type, color,
            supplier, purchase_order, receipt_date, inspection_date, inspection_type,
            total_quantity, sample_quantity, qualified_quantity, defect_quantity,
            defect_issues, inspector, batch_number
        ))

        inspection_id = cursor.lastrowid
        conn.commit()

        return jsonify({
            "success": True,
            "message": "检验记录保存成功",
            "report_code": report_code,
            "inspection_id": inspection_id
        })

    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取检验记录API
@incoming_inspection_bp.route('/api/inspections', methods=['GET'])
def get_inspections():
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 获取筛选参数
        inspection_type = request.args.get('inspection_type', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        material_number = request.args.get('material_number', '')
        supplier = request.args.get('supplier', '')
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []
        
        if inspection_type:
            conditions.append("inspection_type = %s")
            params.append(inspection_type)
            
        if start_date:
            conditions.append("inspection_date >= %s")
            params.append(start_date)
            
        if end_date:
            conditions.append("inspection_date <= %s")
            params.append(end_date)
            
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
            
        if supplier:
            conditions.append("supplier LIKE %s")
            params.append(f"%{supplier}%")
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 计算总记录数
        count_query = f"SELECT COUNT(*) as total FROM incoming_inspection {where_clause}"
        cursor.execute(count_query, params)
        total_records = cursor.fetchone()['total']
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        # 获取分页后的记录
        query = f"""
            SELECT * FROM incoming_inspection 
            {where_clause}
            ORDER BY inspection_date DESC
            LIMIT %s OFFSET %s
        """
        cursor.execute(query, params + [per_page, offset])
        records = cursor.fetchall()
        
        # 转换日期格式
        for record in records:
            if record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({
            "success": True,
            "records": records,
            "total_records": total_records,
            "page": page,
            "per_page": per_page,
            "total_pages": (total_records + per_page - 1) // per_page
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取最近检验记录API
@incoming_inspection_bp.route('/api/recent_inspections')
def recent_inspections():
    try:
        days = request.args.get('days', 30, type=int)
        limit = request.args.get('limit', 10, type=int)
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询统一检验记录
        cursor.execute("""
            SELECT inspection_type, id, material_number, material_name, supplier, inspection_date, 
                   total_quantity, qualified_quantity, defect_quantity, sample_quantity
            FROM incoming_inspection
            WHERE inspection_date BETWEEN %s AND %s
            ORDER BY inspection_date DESC
            LIMIT %s
        """, (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'), limit))
        
        records = cursor.fetchall()
        
        # 转换日期格式
        for record in records:
            record['inspection_date'] = record['inspection_date'].isoformat()
        
        return jsonify({"success": True, "records": records})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
