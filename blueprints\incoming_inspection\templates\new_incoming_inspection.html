{% extends "base.html" %}

{% block title %}新增来料检验记录 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .inspection-form-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .form-section {
        background: #fafafa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #e0e0e0;
    }
    
    .form-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 15px;
        padding-bottom: 5px;
        border-bottom: 2px solid #1976d2;
    }
    
    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .form-group {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
    
    .required {
        color: red;
    }
    
    .inspection-type-section {
        background: #e3f2fd;
        border: 2px solid #1976d2;
    }
    
    .quantity-fields {
        display: none;
    }
    
    .quantity-fields.show {
        display: block;
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-primary {
        background-color: #1976d2;
        color: white;
    }
    
    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-success {
        background-color: #28a745;
        color: white;
    }
    
    .form-actions {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ddd;
    }
    
    .form-actions .btn {
        margin: 0 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="inspection-form-container">
    <h1>新增来料检验记录</h1>
    
    <form id="inspection-form" method="POST" action="/incoming/api/save_inspection">
        <!-- 检验类型选择 -->
        <div class="form-section inspection-type-section">
            <div class="form-section-title">检验类型</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="inspection_type">检验类型 <span class="required">*</span></label>
                    <select id="inspection_type" name="inspection_type" required onchange="toggleQuantityFields()">
                        <option value="">请选择检验类型</option>
                        <option value="sampling">抽样检验</option>
                        <option value="full">全部检验</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 物料信息 -->
        <div class="form-section">
            <div class="form-section-title">物料信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="material_number">物料料号 <span class="required">*</span></label>
                    <input type="text" id="material_number" name="material_number" required>
                </div>
                <div class="form-group">
                    <label for="material_name">物料名称 <span class="required">*</span></label>
                    <input type="text" id="material_name" name="material_name" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="specification">规格</label>
                    <input type="text" id="specification" name="specification">
                </div>
                <div class="form-group">
                    <label for="material_type">材质</label>
                    <input type="text" id="material_type" name="material_type">
                </div>
                <div class="form-group">
                    <label for="color">颜色</label>
                    <input type="text" id="color" name="color">
                </div>
            </div>
        </div>
        
        <!-- 检验信息 -->
        <div class="form-section">
            <div class="form-section-title">检验信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="supplier">供应商 <span class="required">*</span></label>
                    <input type="text" id="supplier" name="supplier" required>
                </div>
                <div class="form-group">
                    <label for="purchase_order">采购单号 <span class="required">*</span></label>
                    <input type="text" id="purchase_order" name="purchase_order" required>
                </div>
                <div class="form-group">
                    <label for="batch_number">批次号</label>
                    <input type="text" id="batch_number" name="batch_number">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="receipt_date">收货日期 <span class="required">*</span></label>
                    <input type="date" id="receipt_date" name="receipt_date" required>
                </div>
                <div class="form-group">
                    <label for="inspection_date">检验日期 <span class="required">*</span></label>
                    <input type="date" id="inspection_date" name="inspection_date" required>
                </div>
                <div class="form-group">
                    <label for="inspector">检验员</label>
                    <input type="text" id="inspector" name="inspector">
                </div>
            </div>
        </div>
        
        <!-- 数量信息 -->
        <div class="form-section">
            <div class="form-section-title">数量信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="total_quantity">总数量 <span class="required">*</span></label>
                    <input type="number" id="total_quantity" name="total_quantity" required min="1">
                </div>
                <div class="form-group quantity-fields" id="sampling-fields">
                    <label for="sample_quantity">抽样数量 <span class="required">*</span></label>
                    <input type="number" id="sample_quantity" name="sample_quantity" min="1">
                </div>
                <div class="form-group quantity-fields" id="full-fields">
                    <label for="qualified_quantity">合格数量 <span class="required">*</span></label>
                    <input type="number" id="qualified_quantity" name="qualified_quantity" min="0">
                </div>
                <div class="form-group">
                    <label for="defect_quantity">不良数量 <span class="required">*</span></label>
                    <input type="number" id="defect_quantity" name="defect_quantity" required min="0" value="0">
                </div>
            </div>
        </div>
        
        <!-- 缺陷信息 -->
        <div class="form-section">
            <div class="form-section-title">缺陷信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label for="defect_issues">缺陷问题描述</label>
                    <textarea id="defect_issues" name="defect_issues" rows="4" placeholder="请描述发现的缺陷问题..."></textarea>
                </div>
            </div>
        </div>
        
        <!-- 表单操作按钮 -->
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="history.back()">取消</button>
            <button type="submit" class="btn btn-success">保存检验记录</button>
        </div>
    </form>
</div>

<script>
// 设置默认日期
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('inspection_date').value = today;
    document.getElementById('receipt_date').value = today;
});

// 切换数量字段显示
function toggleQuantityFields() {
    const inspectionType = document.getElementById('inspection_type').value;
    const samplingFields = document.getElementById('sampling-fields');
    const fullFields = document.getElementById('full-fields');
    const sampleQuantityInput = document.getElementById('sample_quantity');
    const qualifiedQuantityInput = document.getElementById('qualified_quantity');
    
    // 隐藏所有字段
    samplingFields.classList.remove('show');
    fullFields.classList.remove('show');
    
    // 清除required属性
    sampleQuantityInput.removeAttribute('required');
    qualifiedQuantityInput.removeAttribute('required');
    
    if (inspectionType === 'sampling') {
        samplingFields.classList.add('show');
        sampleQuantityInput.setAttribute('required', 'required');
    } else if (inspectionType === 'full') {
        fullFields.classList.add('show');
        qualifiedQuantityInput.setAttribute('required', 'required');
    }
}

// 表单提交处理
document.getElementById('inspection-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/incoming/api/save_inspection', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('检验记录保存成功！报告编码：' + data.report_code);
            // 跳转到检验记录列表
            window.location.href = '/incoming/records';
        } else {
            alert('保存失败：' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请重试');
    });
});

// 物料料号输入时自动获取物料信息
document.getElementById('material_number').addEventListener('blur', function() {
    const materialNumber = this.value.trim();
    if (materialNumber) {
        fetch(`/incoming/api/material_info/${materialNumber}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.material) {
                    const material = data.material;
                    document.getElementById('material_name').value = material.material_name || '';
                    document.getElementById('specification').value = material.specification || '';
                    document.getElementById('material_type').value = material.material_type || '';
                    document.getElementById('color').value = material.color || '';
                }
            })
            .catch(error => {
                console.error('Error fetching material info:', error);
            });
    }
});
</script>
{% endblock %}
