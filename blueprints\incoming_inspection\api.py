from flask import jsonify, request, current_app
from . import incoming_inspection_bp
from db_config import get_db_connection
import os
import sys
import json
from datetime import datetime, timedelta

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))

# 导入工具函数
try:
    from file_utils import get_safe_upload_path, validate_file_size, get_system_setting
except ImportError:
    # 如果导入失败，定义简单的备用函数
    def get_safe_upload_path(base_path, sub_dirs=None):
        if sub_dirs:
            full_path = os.path.join(base_path, *sub_dirs)
        else:
            full_path = base_path
        os.makedirs(full_path, exist_ok=True)
        return full_path

    def validate_file_size(file, max_size_mb=5):
        return True  # 简单实现

    def get_system_setting(key, default=None):
        return default

# 保存统一检验记录API
@incoming_inspection_bp.route('/api/save_inspection', methods=['POST'])
def save_inspection():
    try:
        # 获取基本表单数据
        material_number = request.form.get('material_number')
        material_name = request.form.get('material_name')
        specification = request.form.get('specification')
        material_type = request.form.get('material_type')
        color = request.form.get('color')
        supplier = request.form.get('supplier')
        purchase_order = request.form.get('purchase_order')
        receipt_date = request.form.get('receipt_date')
        inspection_date = request.form.get('inspection_date')
        batch_number = request.form.get('batch_number', '')
        inspection_type = request.form.get('inspection_type', 'sampling')  # 检验类型
        total_quantity = int(request.form.get('total_quantity'))
        defect_quantity = int(request.form.get('defect_quantity'))

        # 根据检验类型获取不同的数量字段
        if inspection_type == 'sampling':
            sample_quantity = int(request.form.get('sample_quantity'))
            qualified_quantity = sample_quantity - defect_quantity
        else:  # full
            qualified_quantity = int(request.form.get('qualified_quantity'))
            sample_quantity = None
            
        defect_issues = request.form.get('defect_issues')
        inspector = request.form.get('inspector')
        
        # 生成报告编码
        from report_code_generator import generate_report_code
        report_code = generate_report_code(inspection_type, inspection_date)

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入到统一的来料检验表
        cursor.execute("""
            INSERT INTO incoming_inspection (
                report_code, material_number, material_name, specification, material_type, color,
                supplier, purchase_order, receipt_date, inspection_date, inspection_type,
                total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                defect_issues, inspector, batch_number
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            report_code, material_number, material_name, specification, material_type, color,
            supplier, purchase_order, receipt_date, inspection_date, inspection_type,
            total_quantity, sample_quantity, qualified_quantity, defect_quantity,
            defect_issues, inspector, batch_number
        ))

        inspection_id = cursor.lastrowid
        conn.commit()

        return jsonify({
            "success": True,
            "message": "检验记录保存成功",
            "report_code": report_code,
            "inspection_id": inspection_id
        })

    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取检验记录详情API
@incoming_inspection_bp.route('/api/inspection_details/<int:record_id>', methods=['GET'])
def get_inspection_details(record_id):
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        cursor.execute("SELECT * FROM incoming_inspection WHERE id = %s", (record_id,))
        record = cursor.fetchone()
        
        if not record:
            return jsonify({"success": False, "error": "记录不存在"}), 404
        
        # 转换日期格式
        if record['receipt_date']:
            record['receipt_date'] = record['receipt_date'].isoformat()
        if record['inspection_date']:
            record['inspection_date'] = record['inspection_date'].isoformat()
        if record['created_at']:
            record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({"success": True, "record": record})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 高级搜索API
@incoming_inspection_bp.route('/api/search', methods=['GET'])
def advanced_search():
    # 获取搜索参数
    material_number = request.args.get('material_number', '')
    material_name = request.args.get('material_name', '')
    supplier = request.args.get('supplier', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    purchase_order = request.args.get('purchase_order', '')
    inspection_type = request.args.get('inspection_type', '')  # 检验类型筛选
    
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if material_number:
            conditions.append("material_number LIKE %s")
            params.append(f"%{material_number}%")
        
        if material_name:
            conditions.append("material_name LIKE %s")
            params.append(f"%{material_name}%")
        
        if supplier:
            conditions.append("supplier LIKE %s")
            params.append(f"%{supplier}%")
        
        if start_date:
            conditions.append("inspection_date >= %s")
            params.append(start_date)
        
        if end_date:
            conditions.append("inspection_date <= %s")
            params.append(end_date)
        
        if purchase_order:
            conditions.append("purchase_order LIKE %s")
            params.append(f"%{purchase_order}%")
            
        if inspection_type:
            conditions.append("inspection_type = %s")
            params.append(inspection_type)
        
        # 组合查询条件
        where_clause = ""
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
        
        # 执行查询
        query = f"""
            SELECT * FROM incoming_inspection
            {where_clause}
            ORDER BY inspection_date DESC
            LIMIT 100
        """
        cursor.execute(query, params)
        records = cursor.fetchall()
        
        # 转换日期格式以便JSON序列化
        for record in records:
            if 'receipt_date' in record and record['receipt_date']:
                record['receipt_date'] = record['receipt_date'].isoformat()
            if 'inspection_date' in record and record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].isoformat()
            if 'created_at' in record and record['created_at']:
                record['created_at'] = record['created_at'].isoformat()
        
        return jsonify({"success": True, "records": records})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 获取物料信息API
@incoming_inspection_bp.route('/api/material_info/<material_number>')
def get_material_info(material_number):
    """根据物料料号获取物料信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询物料基本信息
        cursor.execute("""
            SELECT material_number, material_name, specification, material_type, color, description
            FROM materials 
            WHERE material_number = %s
        """, (material_number,))
        
        material = cursor.fetchone()
        
        if material:
            return jsonify({
                "success": True,
                "material": material
            })
        else:
            return jsonify({
                "success": False,
                "error": "物料不存在"
            })
            
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 获取供应商列表API
@incoming_inspection_bp.route('/api/suppliers')
def get_suppliers():
    """获取所有供应商列表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("SELECT DISTINCT supplier FROM incoming_inspection WHERE supplier IS NOT NULL AND supplier != '' ORDER BY supplier")
        suppliers = [row['supplier'] for row in cursor.fetchall()]
        
        return jsonify({"success": True, "suppliers": suppliers})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 删除检验记录API
@incoming_inspection_bp.route('/api/delete_inspection/<int:record_id>', methods=['DELETE'])
def delete_inspection(record_id):
    """删除检验记录"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM incoming_inspection WHERE id = %s", (record_id,))
        if not cursor.fetchone():
            return jsonify({"success": False, "error": "记录不存在"}), 404
        
        # 删除记录
        cursor.execute("DELETE FROM incoming_inspection WHERE id = %s", (record_id,))
        conn.commit()
        
        return jsonify({"success": True, "message": "记录删除成功"})
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
